#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plan A Classifier: Filter-Bank Riemannian + Tangent Space LR (with simple fusion)

设计目标：
- 6子带带通 → 每带协方差（OAS/LW收缩）
- 分支A：MDM（按带训练，预测时跨带距离求和后softmax）
- 分支B：Tangent Space + Logistic Regression（各带切空间拼接）
- 概率校准（占位：温度缩放，初始T=1.0）
- 融合：加权平均（默认 mdm:tslr = 0.6:0.4）

说明：为保证与现有系统快速集成，本实现不依赖原先的特征提取模块，完全自包含。
"""

from __future__ import annotations

import json
import pickle
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Sequence, Tuple

import numpy as np
from scipy import signal

try:
    from pyriemann.estimation import Covariances
    from pyriemann.tangentspace import TangentSpace
    from pyriemann.classification import MDM
    PYRIEMANN_AVAILABLE = True
except Exception:  # pragma: no cover - 环境兜底
    PYRIEMANN_AVAILABLE = False
    Covariances = None  # type: ignore
    TangentSpace = None  # type: ignore
    MDM = None  # type: ignore

from sklearn.linear_model import LogisticRegression
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.decomposition import PCA


@dataclass
class PlanAConfig:
    sampling_rate: int = 125
    window_samples: int = 250  # 2秒窗口
    # 6子带（可按需调整）
    subbands: List[Tuple[float, float]] = (
        (8.0, 12.0), (12.0, 16.0), (16.0, 20.0), (20.0, 24.0), (24.0, 28.0), (28.0, 32.0)
    )
    covariance_estimator: str = "oas"  # 'oas' | 'lwf' | 'cov'
    riemann_metric: str = "riemann"  # 'riemann' | 'logeuclid'
    fusion_weights: Dict[str, float] = None  # {'mdm': 0.6, 'tslr': 0.4}
    ts_classifier: str = "lda"               # 'lda' | 'lr'
    ts_lr_C: float = 0.2                     # 仅当 ts_classifier='lr' 有效
    ts_lr_penalty: str = "l2"                # 'l2' | 'elasticnet'（若换成saga+elasticnet）
    ts_lr_class_weight: Optional[str] = "balanced"
    ts_band_weights: Optional[List[float]] = None  # 每子带中层融合权重
    # 可选：每通道权重（长度需与通道数一致），用于在计算协方差前对通道缩放
    channel_weights: Optional[List[float]] = None

    # 集成学习配置
    # 🔧 Linus修复：重新启用集成分类器，但降低有问题算法的权重
    enable_ensemble: bool = True  # 是否启用多分类器集成
    ensemble_weights: Optional[Dict[str, float]] = None  # 集成权重
    fbcsp_n_components: int = 4  # FBCSP每子带CSP成分数
    wavelet_name: str = "morl"   # 小波类型：'morl'(Morlet), 'cmor'(Complex Morlet)
    wavelet_freq_range: Tuple[float, float] = (8.0, 30.0)  # 小波频率范围
    spectral_bands: List[Tuple[float, float]] = None  # 频谱特征频段

    # 训练时预处理一致性配置
    # 🔧 Linus修复：强制启用标准化，确保训练和实时一致
    ensemble_use_session_norm: bool = True  # 集成分类器训练时是否使用会话标准化

    def __post_init__(self) -> None:
        if self.fusion_weights is None:
            self.fusion_weights = {"mdm": 0.6, "tslr": 0.4}
        # 集成权重默认配置
        if self.ensemble_weights is None:
            if self.enable_ensemble:
                # 🔧 Linus修复：平衡权重，让修复后的算法有机会表现
                self.ensemble_weights = {
                    "mdm": 0.30, "tslr": 0.30, "fbcsp": 0.25,
                    "wavelet": 0.075, "spectral": 0.075
                }
            else:
                self.ensemble_weights = {"mdm": 0.6, "tslr": 0.4}
        # 频谱特征频段
        if self.spectral_bands is None:
            self.spectral_bands = [
                (8.0, 13.0),   # Alpha
                (13.0, 20.0),  # Low Beta
                (20.0, 30.0),  # High Beta
                (30.0, 40.0)   # Low Gamma
            ]
        # 子带默认权重：下调高β带（示例：6带情形）
        if self.ts_band_weights is None:
            if len(self.subbands) == 6:
                # 低频→高频：保持前两带权重，逐步降低高频带
                self.ts_band_weights = [0.8, 1.0, 0.9, 0.8, 0.6, 0.4]
            else:
                self.ts_band_weights = [1.0] * len(self.subbands)
        # 通道默认权重：突出运动皮层C3/C4（默认通道顺序: ['PZ','P3','P4','C3','CZ','C4','F3','F4']）
        # 🔧 Linus修复：突出运动皮层，降低额叶权重
        if self.channel_weights is None:
            self.channel_weights = [1.0, 1.0, 1.0, 1.2, 1.0, 1.2, 0.8, 0.8]


class _TemperatureScaler:
    """简单温度缩放（占位实现：T固定为1.0，可后续扩展为基于CV优化）。"""

    def __init__(self, temperature: float = 1.0) -> None:
        self.temperature = max(1e-6, float(temperature))

    def fit(self, probs: np.ndarray, y: np.ndarray) -> None:
        # 可扩展：通过最小化NLL优化T；当前先保持T=1.0以保证稳定
        self.temperature = 1.0

    def transform(self, probs: np.ndarray) -> np.ndarray:
        # 将概率通过softmax温度缩放；此处直接对logits近似处理：p^(1/T)/Z
        p = np.clip(probs, 1e-9, 1 - 1e-9)
        if self.temperature == 1.0:
            return p
        # 简单幂缩放（近似）：
        p1 = p ** (1.0 / self.temperature)
        p0 = (1.0 - p) ** (1.0 / self.temperature)
        z = p1 + p0
        return p1 / np.clip(z, 1e-9, None)


class _CSPTransformer:
    """CSP空间滤波器"""

    def __init__(self, n_components: int = 4) -> None:
        self.n_components = n_components
        self.filters_: Optional[np.ndarray] = None
        self.eigenvals_: Optional[np.ndarray] = None

    def fit(self, X: np.ndarray, y: np.ndarray) -> '_CSPTransformer':
        """训练CSP滤波器

        Args:
            X: [n_trials, n_channels, n_samples]
            y: [n_trials] 二分类标签 {0, 1}
        """
        n_trials, n_channels, n_samples = X.shape

        # 计算每类的平均协方差矩阵
        cov_0 = np.zeros((n_channels, n_channels))
        cov_1 = np.zeros((n_channels, n_channels))
        n_0 = n_1 = 0

        for i in range(n_trials):
            cov_trial = np.cov(X[i])
            if y[i] == 0:
                cov_0 += cov_trial
                n_0 += 1
            else:
                cov_1 += cov_trial
                n_1 += 1

        if n_0 > 0:
            cov_0 /= n_0
        if n_1 > 0:
            cov_1 /= n_1

        # 广义特征值分解
        try:
            # 使用scipy.linalg.eigh避免numpy的UPLO参数问题
            from scipy.linalg import eigh
            eigenvals, eigenvecs = eigh(cov_1, cov_0 + cov_1 + 1e-6 * np.eye(n_channels))
            # 排序：选择最大和最小的特征值对应的特征向量
            idx = np.argsort(eigenvals)
            # 选择前n_components/2个最小和最大的
            n_select = min(self.n_components // 2, n_channels // 2)
            selected_idx = np.concatenate([idx[:n_select], idx[-n_select:]])

            self.filters_ = eigenvecs[:, selected_idx].T  # [n_components, n_channels]
            self.eigenvals_ = eigenvals[selected_idx]

        except np.linalg.LinAlgError:
            # 备用方案：使用PCA
            pca = PCA(n_components=min(self.n_components, n_channels))
            X_flat = X.reshape(n_trials, -1)
            pca.fit(X_flat)
            # 构造伪CSP滤波器
            self.filters_ = pca.components_[:self.n_components, :n_channels]
            self.eigenvals_ = pca.explained_variance_ratio_[:self.n_components]

        return self

    def transform(self, X: np.ndarray) -> np.ndarray:
        """应用CSP滤波

        Args:
            X: [n_trials, n_channels, n_samples] 或 [n_channels, n_samples]
        Returns:
            features: [n_trials, n_components] 或 [n_components]
        """
        if self.filters_ is None:
            raise ValueError("CSP未训练")

        single_trial = False
        if X.ndim == 2:
            X = X[np.newaxis, :, :]
            single_trial = True

        n_trials, n_channels, n_samples = X.shape
        features = np.zeros((n_trials, self.filters_.shape[0]))

        for i in range(n_trials):
            # 空间滤波
            filtered = self.filters_ @ X[i]  # [n_components, n_samples]
            # 计算对数方差作为特征
            var_features = np.var(filtered, axis=1)
            features[i] = np.log(var_features + 1e-8)

        if single_trial:
            return features[0]
        return features


class _SubbandFilterBank:
    """IIR子带滤波器组（Butterworth，sos实现）。"""

    def __init__(self, fs: int, bands: Sequence[Tuple[float, float]], order: int = 4) -> None:
        self.fs = fs
        self.bands = list(bands)
        self.order = order
        self._sos_list: List[np.ndarray] = []
        for low, high in self.bands:
            sos = signal.butter(
                N=self.order,
                Wn=[low, high],
                btype="bandpass",
                fs=self.fs,
                output="sos",
            )
            self._sos_list.append(sos)

    def transform(self, x: np.ndarray) -> List[np.ndarray]:
        """
        Args:
            x: [n_channels, n_samples]
        Returns:
            List of bandpassed arrays, each shape [n_channels, n_samples]
        """
        outputs: List[np.ndarray] = []
        for sos in self._sos_list:
            # 使用filtfilt以获得零相位失真
            y = signal.sosfiltfilt(sos, x, axis=1)
            outputs.append(y.astype(np.float32, copy=False))
        return outputs


class _WaveletFeatureExtractor:
    """小波时频特征提取器"""

    def __init__(self, fs: int = 125, wavelet: str = "morl",
                 freq_range: Tuple[float, float] = (8.0, 30.0)) -> None:
        self.fs = fs
        self.wavelet = wavelet
        self.freq_range = freq_range
        # 生成频率尺度 - 🔧 Linus修复：减少频率点，降低特征维度
        self.freqs = np.linspace(freq_range[0], freq_range[1], 10)  # 从20减少到10
        self.scales = self.fs / (self.freqs * 2)  # 近似尺度转换

    def extract_features(self, X: np.ndarray) -> np.ndarray:
        """提取小波时频特征

        Args:
            X: [n_trials, n_channels, n_samples] 或 [n_channels, n_samples]
        Returns:
            features: [n_trials, n_features] 或 [n_features]
        """
        try:
            import pywt
        except ImportError:
            # 备用方案：使用频谱特征
            return self._extract_spectral_fallback(X)

        single_trial = False
        if X.ndim == 2:
            X = X[np.newaxis, :, :]
            single_trial = True

        n_trials, n_channels, n_samples = X.shape
        features_list = []

        for i in range(n_trials):
            trial_features = []
            for ch in range(n_channels):
                signal_ch = X[i, ch, :]
                # 连续小波变换
                coeffs, _ = pywt.cwt(signal_ch, self.scales, self.wavelet)
                # 计算时频能量特征
                energy = np.abs(coeffs) ** 2
                # 🔧 Linus修复：只使用均值特征，减少特征维度避免过拟合
                mean_energy = np.mean(energy, axis=1)  # 每个频率的平均能量

                trial_features.append(mean_energy)

            # 展平所有特征
            features_trial = np.concatenate([f.flatten() for f in trial_features])
            features_list.append(features_trial)

        features = np.array(features_list)
        if single_trial:
            return features[0]
        return features

    def _extract_spectral_fallback(self, X: np.ndarray) -> np.ndarray:
        """备用频谱特征提取"""
        single_trial = False
        if X.ndim == 2:
            X = X[np.newaxis, :, :]
            single_trial = True

        n_trials, n_channels, n_samples = X.shape
        features_list = []

        for i in range(n_trials):
            trial_features = []
            for ch in range(n_channels):
                signal_ch = X[i, ch, :]
                # 功率谱密度
                freqs, psd = signal.welch(signal_ch, fs=self.fs, nperseg=min(128, n_samples//2))
                # 选择感兴趣的频段
                freq_mask = (freqs >= self.freq_range[0]) & (freqs <= self.freq_range[1])
                psd_band = psd[freq_mask]

                # 统计特征
                trial_features.extend([
                    np.mean(psd_band),
                    np.var(psd_band),
                    np.max(psd_band),
                    np.sum(psd_band)
                ])

            features_list.append(np.array(trial_features))

        features = np.array(features_list)
        if single_trial:
            return features[0]
        return features


class _SpectralFeatureExtractor:
    """频谱功率特征提取器"""

    def __init__(self, fs: int = 125, bands: Optional[List[Tuple[float, float]]] = None) -> None:
        self.fs = fs
        if bands is None:
            self.bands = [
                (8.0, 13.0),   # Alpha
                (13.0, 20.0),  # Low Beta
                (20.0, 30.0),  # High Beta
                (30.0, 40.0)   # Low Gamma
            ]
        else:
            self.bands = bands

    def extract_features(self, X: np.ndarray) -> np.ndarray:
        """提取频谱功率特征

        Args:
            X: [n_trials, n_channels, n_samples] 或 [n_channels, n_samples]
        Returns:
            features: [n_trials, n_features] 或 [n_features]
        """
        single_trial = False
        if X.ndim == 2:
            X = X[np.newaxis, :, :]
            single_trial = True

        n_trials, n_channels, n_samples = X.shape
        features_list = []

        for i in range(n_trials):
            trial_features = []
            for ch in range(n_channels):
                signal_ch = X[i, ch, :]
                # 功率谱密度
                freqs, psd = signal.welch(signal_ch, fs=self.fs, nperseg=min(128, n_samples//2))

                # 计算各频段功率
                band_powers = []
                for low, high in self.bands:
                    freq_mask = (freqs >= low) & (freqs <= high)
                    band_power = np.sum(psd[freq_mask])
                    band_powers.append(band_power)

                # 相对功率（归一化）
                total_power = sum(band_powers) + 1e-8
                rel_powers = [p / total_power for p in band_powers]

                # 功率比特征
                if len(band_powers) >= 2:
                    power_ratios = [band_powers[i] / (band_powers[j] + 1e-8)
                                   for i in range(len(band_powers))
                                   for j in range(i+1, len(band_powers))]
                else:
                    power_ratios = []

                trial_features.extend(band_powers + rel_powers + power_ratios)

            features_list.append(np.array(trial_features))

        features = np.array(features_list)
        if single_trial:
            return features[0]
        return features


class PlanAClassifier:
    """自包含的方案A分类器实现。"""

    def __init__(self, config: Optional[PlanAConfig] = None) -> None:
        if config is None:
            config = PlanAConfig()
        self.config = config

        if not PYRIEMANN_AVAILABLE:
            raise ImportError("需要安装pyriemann库以使用方案A分类器")

        self._bank = _SubbandFilterBank(fs=config.sampling_rate, bands=config.subbands)
        self._cov = Covariances(estimator=config.covariance_estimator)
        self._ts = TangentSpace(metric=config.riemann_metric)

        # 分支A：每个子带一个MDM
        self._mdm_models: List[MDM] = []
        # 分支B（改造）：每子带独立 TangentSpace + 分类器（中层融合）
        self._ts_band: List[TangentSpace] = []
        self._ts_scalers: List[StandardScaler] = []
        self._ts_clfs: List[Any] = []

        # 集成分类器分支
        if config.enable_ensemble:
            self._fbcsp_models: List[Any] = []  # FBCSP分类器（每子带）
            self._fbcsp_scalers: List[StandardScaler] = []
            self._wavelet_extractor = _WaveletFeatureExtractor(
                fs=config.sampling_rate,
                wavelet=config.wavelet_name,
                freq_range=config.wavelet_freq_range
            )
            self._spectral_extractor = _SpectralFeatureExtractor(
                fs=config.sampling_rate,
                bands=config.spectral_bands
            )
            self._wavelet_model: Optional[Any] = None  # 小波特征分类器
            self._wavelet_scaler: Optional[StandardScaler] = None
            self._spectral_model: Optional[Any] = None  # 频谱特征分类器
            self._spectral_scaler: Optional[StandardScaler] = None
        else:
            self._fbcsp_models = []
            self._fbcsp_scalers = []
            self._wavelet_extractor = None
            self._spectral_extractor = None
            self._wavelet_model = None
            self._wavelet_scaler = None
            self._spectral_model = None
            self._spectral_scaler = None

        # 温度缩放器
        self._temp_mdm = _TemperatureScaler(1.0)
        self._temp_tslr = _TemperatureScaler(1.0)
        if config.enable_ensemble:
            self._temp_fbcsp = _TemperatureScaler(1.0)
            self._temp_wavelet = _TemperatureScaler(1.0)
            self._temp_spectral = _TemperatureScaler(1.0)
        else:
            self._temp_fbcsp = None
            self._temp_wavelet = None
            self._temp_spectral = None

        # 训练状态
        self._is_fitted = False

        # 通道权重：若提供，在预处理协方差前对通道作缩放
        self._channel_weights_vec: Optional[np.ndarray] = None
        if getattr(self.config, "channel_weights", None) is not None:
            try:
                w = np.asarray(self.config.channel_weights, dtype=np.float64).ravel()
                if np.all(w > 0):
                    self._channel_weights_vec = w
            except Exception:
                self._channel_weights_vec = None

        # 会话对齐（EA/RA）
        self._align_mode: Optional[str] = None  # 'ea' | 'ra' | None
        self._ea_whitener: Optional[np.ndarray] = None  # [C, C]
        self._ra_whitener: Optional[np.ndarray] = None  # [C, C]

    # -------------------- 工具函数 --------------------
    @staticmethod
    def _softmax_neg_dist(dist_2d: np.ndarray) -> np.ndarray:
        """将每样本[2类距离]转为概率，使用softmax(-d)。"""
        # 🔧 Linus修复：减少裁剪范围，让距离差异更明显
        logits = -np.clip(dist_2d, -100, 100)  # 从1e6改为100
        logits = logits - np.max(logits, axis=1, keepdims=True)
        e = np.exp(logits)
        p = e / np.sum(e, axis=1, keepdims=True)
        # 返回MI类别概率（索引1）
        return p[:, 1]

    def _stack_tangent_features(self, X: np.ndarray) -> np.ndarray:
        """对每个trial做6子带切空间特征并拼接。

        Args:
            X: [n_trials, n_channels, n_samples]
        Returns:
            features: [n_trials, n_features_total]
        """
        n_trials, n_channels, _ = X.shape
        feats: List[np.ndarray] = []
        for i in range(n_trials):
            band_signals = self._bank.transform(X[i])  # List[[C, T]]
            band_feats: List[np.ndarray] = []
            for b in band_signals:
                cov = self._cov.fit_transform(b[np.newaxis, :, :])  # [1, C, C]
                # 使用已拟合的切空间进行变换（避免fit_transform导致零向量）
                f = self._ts.transform(cov)  # [1, C*(C+1)/2]
                band_feats.append(f)
            feats.append(np.concatenate(band_feats, axis=1))
        return np.vstack(feats)

    @staticmethod
    def _sym_posdef_invsqrt(cov: np.ndarray, eps: float = 1e-9) -> np.ndarray:
        """计算对称正定矩阵的逆平方根（数值稳定）。

        Args:
            cov: [C, C]
        Returns:
            W: [C, C], 满足 W @ cov @ W^T ≈ I
        """
        # 特征分解
        vals, vecs = np.linalg.eigh((cov + cov.T) * 0.5)
        vals = np.clip(vals, eps, None)
        inv_sqrt = np.diag(1.0 / np.sqrt(vals))
        return (vecs @ inv_sqrt) @ vecs.T

    # -------------------- 会话对齐/温度校准 --------------------
    def calibrate_session(self, X_cal: np.ndarray, y_cal: Optional[np.ndarray] = None, mode: str = "ea") -> Dict[str, Any]:
        """基于会话（≤1分钟）数据进行对齐（EA或RA）。

        Args:
            X_cal: [n_trials, n_channels, window_samples]
            y_cal: 可选标签（未使用）
            mode: 'ea'（默认，对信号白化）或 'ra'（对协方差共轭变换）
        Returns:
            info: 对齐信息（模式、条件数等）
        """
        if X_cal.ndim != 3 or X_cal.shape[2] != self.config.window_samples:
            raise ValueError("X_cal 维度应为 [n_trials, n_channels, window_samples]")

        # 计算通道层面的均值协方差（不分子带），保持与在线路径相同的通道缩放
        n_trials, n_channels, _ = X_cal.shape
        cov_sum = np.zeros((n_channels, n_channels), dtype=np.float64)
        for i in range(n_trials):
            x = X_cal[i]
            if self._channel_weights_vec is not None and self._channel_weights_vec.shape[0] == x.shape[0]:
                x = (self._channel_weights_vec[:, None] * x)
            # 🔧 Linus修复：去均值后计算协方差
            x_centered = x - np.mean(x, axis=1, keepdims=True)
            c = np.cov(x_centered, bias=False)
            cov_sum += c
        cov_mean = cov_sum / max(1, n_trials)

        W = self._sym_posdef_invsqrt(cov_mean)

        mode_l = (mode or "").lower().strip()
        if mode_l == "ra":
            self._align_mode = "ra"
            self._ra_whitener = W.astype(np.float64, copy=True)
            self._ea_whitener = None
        else:
            self._align_mode = "ea"
            self._ea_whitener = W.astype(np.float64, copy=True)
            self._ra_whitener = None

        # 返回基本信息
        info = {
            "alignment": {
                "mode": self._align_mode,
                "enabled": True,
            }
        }
        return info

    def calibrate_temperature(self, X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, float]:
        """用少量带标签的数据对 MDM/TS 概率做温度缩放校准（最小化NLL）。"""
        if X_val.ndim != 3 or X_val.shape[2] != self.config.window_samples:
            raise ValueError("X_val 维度应为 [n_trials, n_channels, window_samples]")
        if y_val.shape[0] != X_val.shape[0]:
            raise ValueError("X_val 与 y_val 数量不一致")

        # 暂存当前温度，置1以获取未校准概率
        old_t_mdm = float(self._temp_mdm.temperature)
        old_t_tslr = float(self._temp_tslr.temperature)
        self._temp_mdm.temperature = 1.0
        self._temp_tslr.temperature = 1.0

        p_mdm_list: List[float] = []
        p_tslr_list: List[float] = []
        for i in range(X_val.shape[0]):
            comps = self.predict_proba_components(X_val[i])
            p_mdm_list.append(float(comps.get("p_mdm", 0.5)))
            p_tslr_list.append(float(comps.get("p_tslr", 0.5)))

        p_mdm = np.clip(np.asarray(p_mdm_list, dtype=np.float64), 1e-9, 1 - 1e-9)
        p_tslr = np.clip(np.asarray(p_tslr_list, dtype=np.float64), 1e-9, 1 - 1e-9)
        y = np.asarray(y_val, dtype=np.int64).ravel()

        def nll_after_temp(p: np.ndarray, t: float) -> float:
            # 近似温度缩放：p' = p^(1/T) / (p^(1/T) + (1-p)^(1/T))
            t = float(max(1e-6, t))
            p1 = np.power(p, 1.0 / t)
            p0 = np.power(1.0 - p, 1.0 / t)
            z = p1 + p0
            p_adj = np.clip(p1 / np.clip(z, 1e-12, None), 1e-9, 1 - 1e-9)
            # 二分类NLL
            return float(-np.mean(y * np.log(p_adj) + (1 - y) * np.log(1 - p_adj)))

        # 简单网格搜索 T ∈ [0.5, 3.0]
        grid = np.linspace(0.5, 3.0, num=26)
        t_mdm = float(grid[np.argmin([nll_after_temp(p_mdm, t) for t in grid])])
        t_tslr = float(grid[np.argmin([nll_after_temp(p_tslr, t) for t in grid])])

        # 应用并返回
        self._temp_mdm.temperature = t_mdm
        self._temp_tslr.temperature = t_tslr

        return {"t_mdm": t_mdm, "t_tslr": t_tslr, "t_mdm_old": old_t_mdm, "t_tslr_old": old_t_tslr}

    def _compute_mdm_distances(self, X: np.ndarray) -> np.ndarray:
        """跨子带求和的MDM距离。

        Returns:
            dist_sum: [n_trials, 2]  两类距离之和
        """
        n_trials = X.shape[0]
        dist_sum = np.zeros((n_trials, 2), dtype=np.float64)
        for i in range(n_trials):
            band_signals = self._bank.transform(X[i])
            # 每带一个MDM
            for model, b in zip(self._mdm_models, band_signals):
                # 会话对齐（EA：对白化信号）
                if self._align_mode == "ea" and self._ea_whitener is not None and self._ea_whitener.shape[0] == b.shape[0]:
                    b = (self._ea_whitener @ b)
                # 若配置了通道权重，对通道幅值做缩放（等价于协方差通道加权）
                if self._channel_weights_vec is not None and self._channel_weights_vec.shape[0] == b.shape[0]:
                    b = (self._channel_weights_vec[:, None] * b)
                # 🔧 Linus修复：去均值后计算协方差
                b_centered = b - np.mean(b, axis=1, keepdims=True)
                cov = self._cov.transform(b_centered[np.newaxis, :, :])  # [1, C, C]
                # 会话对齐（RA：对协方差做共轭变换）
                if self._align_mode == "ra" and self._ra_whitener is not None and self._ra_whitener.shape[0] == cov.shape[1]:
                    W = self._ra_whitener
                    cov = (W[None, :, :] @ cov @ W[None, :, :].transpose(0, 2, 1))
                d = model._predict_distances(cov)  # type: ignore  # [1, n_classes]
                dist_sum[i] += d[0]
        return dist_sum

    # -------------------- 训练/预测接口 --------------------
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """训练方案A分类器。

        Args:
            X: [n_trials, n_channels, window_samples]
            y: [n_trials], 二分类 {0: rest, 1: motor imagery}
        """
        if X.ndim != 3 or X.shape[2] != self.config.window_samples:
            raise ValueError("输入X维度应为 [n_trials, n_channels, window_samples]")
        if not set(np.unique(y)).issubset({0, 1}):
            raise ValueError("y 仅支持二分类 {0,1}")

        # 🔧 Linus修复：强制使用全局标准化，确保训练和实时一致
        X_processed = self._apply_global_normalization(X)
        print(f"🔧 训练时强制使用全局标准化")

        # 分支A：为每个子带训练一个MDM
        self._mdm_models = []
        for _ in self.config.subbands:
            self._mdm_models.append(MDM(metric=self.config.riemann_metric))

        # 逐带协方差 → 拟合各自MDM
        # 为避免多次重复滤波，按trial遍历（样本量通常不大）
        covs_per_band: List[List[np.ndarray]] = [[] for _ in self.config.subbands]
        for i in range(X_processed.shape[0]):  # 🔧 使用标准化后的数据
            band_signals = self._bank.transform(X_processed[i])
            for b_idx, b in enumerate(band_signals):
                if self._channel_weights_vec is not None and self._channel_weights_vec.shape[0] == b.shape[0]:
                    b = (self._channel_weights_vec[:, None] * b)
                # 🔧 Linus修复：去均值后计算协方差
                b_centered = b - np.mean(b, axis=1, keepdims=True)
                cov = self._cov.fit_transform(b_centered[np.newaxis, :, :])  # [1, C, C]
                covs_per_band[b_idx].append(cov[0])
        # 堆叠到数组并拟合
        for b_idx, model in enumerate(self._mdm_models):
            cov_band = np.stack(covs_per_band[b_idx], axis=0)  # [n_trials, C, C]

            # 🔧 Linus诊断：检查协方差矩阵的类间差异
            y_array = np.array(y)
            cov_rest = cov_band[y_array == 0]  # 静息态协方差
            cov_mi = cov_band[y_array == 1]    # 运动想象协方差

            if len(cov_rest) > 0 and len(cov_mi) > 0:
                # 计算类间距离
                mean_rest = np.mean(cov_rest, axis=0)
                mean_mi = np.mean(cov_mi, axis=0)
                frobenius_dist = np.linalg.norm(mean_rest - mean_mi, 'fro')
                print(f"🔧 子带{b_idx} 类间协方差Frobenius距离: {frobenius_dist:.6f}")

                # 检查协方差矩阵的条件数
                rest_cond = np.mean([np.linalg.cond(c) for c in cov_rest])
                mi_cond = np.mean([np.linalg.cond(c) for c in cov_mi])
                print(f"🔧 子带{b_idx} 条件数 - 静息: {rest_cond:.2f}, 运动想象: {mi_cond:.2f}")

            model.fit(cov_band, y)

        # 分支B（改造）：每子带 TangentSpace + 分类器（TS-LDA 或 正则LR），并中层融合
        self._ts_band, self._ts_scalers, self._ts_clfs = [], [], []
        for b_idx in range(len(self.config.subbands)):
            cov_band = np.stack(covs_per_band[b_idx], axis=0)  # [n_trials, C, C]
            # 1) 切空间参考：用该带全部训练协方差拟合
            ts = TangentSpace(metric=self.config.riemann_metric)
            ts.fit(cov_band)
            feats_b = ts.transform(cov_band)  # [n_trials, n_feat_b]

            # 2) 每带独立稳健缩放
            scaler_b = StandardScaler()
            feats_b = scaler_b.fit_transform(feats_b)

            # 3) 判别器：默认TS-LDA（Shrinkage LDA更稳）；可选LR（正则+类权重）
            if str(self.config.ts_classifier).lower() == "lda":
                clf_b = LinearDiscriminantAnalysis(solver="lsqr", shrinkage="auto")
            else:
                clf_b = LogisticRegression(
                    C=float(self.config.ts_lr_C),
                    max_iter=2000,
                    solver="lbfgs",
                    penalty=str(self.config.ts_lr_penalty),
                    class_weight=self.config.ts_lr_class_weight,
                    random_state=42,
                )
            clf_b.fit(feats_b, y)

            self._ts_band.append(ts)
            self._ts_scalers.append(scaler_b)
            self._ts_clfs.append(clf_b)

        # 训练集成分类器
        if self.config.enable_ensemble:
            self._train_ensemble_classifiers(X_processed, y)  # 🔧 使用标准化后的数据

        # 温度缩放（占位：T=1.0）
        # 可扩展为：在简单CV上拟合T
        # ---> self._temp_mdm.fit(mdm_p_train, y)
        # ---> self._temp_tslr.fit(tslr_p_train, y)

        self._is_fitted = True

    def _train_ensemble_classifiers(self, X: np.ndarray, y: np.ndarray) -> None:
        """训练集成分类器"""
        try:
            # 决定是否使用会话标准化预处理
            X_processed = X
            if self.config.ensemble_use_session_norm:
                # 使用简单的全局标准化（模拟会话标准化效果）
                X_processed = self._apply_global_normalization(X)
                print(f"✅ 集成分类器训练使用全局标准化")
            else:
                print(f"✅ 集成分类器训练使用原始数据")

            # 1. 训练FBCSP分类器（每子带）
            self._fbcsp_models = []
            self._fbcsp_scalers = []
            print(f"🔧 开始训练集成分类器，数据形状: {X_processed.shape}")

            for b_idx in range(len(self.config.subbands)):
                # 提取子带数据
                band_data = []
                for i in range(X_processed.shape[0]):
                    band_signals = self._bank.transform(X_processed[i])
                    band_data.append(band_signals[b_idx])
                band_X = np.array(band_data)  # [n_trials, n_channels, n_samples]

                # CSP特征提取
                csp = _CSPTransformer(n_components=self.config.fbcsp_n_components)
                csp_features = csp.fit(band_X, y).transform(band_X)

                # 标准化
                scaler = StandardScaler()
                csp_features_scaled = scaler.fit_transform(csp_features)

                # LDA分类器
                lda = LinearDiscriminantAnalysis(solver="lsqr", shrinkage="auto")
                lda.fit(csp_features_scaled, y)

                # 保存模型
                self._fbcsp_models.append((csp, lda))
                self._fbcsp_scalers.append(scaler)

            # 2. 训练小波特征分类器
            if self._wavelet_extractor is not None:
                wavelet_features = self._wavelet_extractor.extract_features(X_processed)
                print(f"🔧 小波特征原始维度: {wavelet_features.shape}")

                # 🔧 Linus修复：检查特征质量，处理异常值
                if np.any(np.isnan(wavelet_features)) or np.any(np.isinf(wavelet_features)):
                    print("⚠️ 小波特征包含NaN/Inf，进行清理")
                    wavelet_features = np.nan_to_num(wavelet_features, nan=0.0, posinf=1.0, neginf=-1.0)

                # 检查特征方差，移除零方差特征
                feature_var = np.var(wavelet_features, axis=0)
                valid_features = feature_var > 1e-8
                if np.sum(valid_features) < wavelet_features.shape[1]:
                    print(f"🔧 移除{np.sum(~valid_features)}个零方差小波特征")
                    wavelet_features = wavelet_features[:, valid_features]

                self._wavelet_scaler = StandardScaler()
                wavelet_features_scaled = self._wavelet_scaler.fit_transform(wavelet_features)

                # 🔧 使用更稳健的分类器，避免过拟合
                self._wavelet_model = LogisticRegression(C=1.0, max_iter=1000, solver="lbfgs", random_state=42)
                self._wavelet_model.fit(wavelet_features_scaled, y)
                print(f"✅ 小波特征分类器训练完成，特征维度: {wavelet_features.shape}")

            # 3. 训练频谱特征分类器
            if self._spectral_extractor is not None:
                spectral_features = self._spectral_extractor.extract_features(X_processed)
                print(f"🔧 频谱特征原始维度: {spectral_features.shape}")

                # 🔧 Linus修复：检查特征质量
                if np.any(np.isnan(spectral_features)) or np.any(np.isinf(spectral_features)):
                    print("⚠️ 频谱特征包含NaN/Inf，进行清理")
                    spectral_features = np.nan_to_num(spectral_features, nan=0.0, posinf=1.0, neginf=-1.0)

                # 检查特征方差
                feature_var = np.var(spectral_features, axis=0)
                valid_features = feature_var > 1e-8
                if np.sum(valid_features) < spectral_features.shape[1]:
                    print(f"🔧 移除{np.sum(~valid_features)}个零方差频谱特征")
                    spectral_features = spectral_features[:, valid_features]

                self._spectral_scaler = StandardScaler()
                spectral_features_scaled = self._spectral_scaler.fit_transform(spectral_features)

                # 🔧 使用更简单的分类器，避免过拟合
                self._spectral_model = LogisticRegression(C=1.0, max_iter=1000, solver="lbfgs", random_state=42)
                self._spectral_model.fit(spectral_features_scaled, y)
                print(f"✅ 频谱特征分类器训练完成，特征维度: {spectral_features.shape}")

        except Exception as e:
            import traceback
            print(f"警告：集成分类器训练失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            # 禁用集成模式
            self.config.enable_ensemble = False

    def _apply_global_normalization(self, X: np.ndarray) -> np.ndarray:
        """应用全局标准化（模拟会话标准化）"""
        # X: [n_trials, n_channels, n_samples]
        # 计算全局均值和标准差
        X_flat = X.reshape(-1, X.shape[1], X.shape[2])  # [n_trials, n_channels, n_samples]

        # 按通道计算统计量
        mean_per_channel = np.mean(X_flat, axis=(0, 2), keepdims=True)  # [1, n_channels, 1]
        std_per_channel = np.std(X_flat, axis=(0, 2), keepdims=True)    # [1, n_channels, 1]
        std_per_channel = np.maximum(std_per_channel, 1e-8)  # 避免除零

        # 标准化
        X_normalized = (X - mean_per_channel) / std_per_channel
        return X_normalized

    def predict_proba(self, X_window: np.ndarray) -> float:
        """返回单窗口MI概率（融合后，不含平滑）。

        Args:
            X_window: [n_channels, window_samples]
        Returns:
            p_mi: float in [0,1]
        """
        comps = self.predict_proba_components(X_window)
        return comps["p"]

    def predict_proba_components(self, X_window: np.ndarray) -> Dict[str, float]:
        """返回单窗口的分支概率与融合概率。

        Args:
            X_window: [n_channels, window_samples] 或 [1, n_channels, window_samples]
        Returns:
            dict: 包含各分支概率和融合概率的字典
        """
        if not self._is_fitted:
            raise ValueError("PlanAClassifier 未训练")
        # 兼容输入：[8, 250] 或 [1, 8, 250]
        xw = X_window
        if xw.ndim == 3 and xw.shape[0] == 1:
            xw = xw[0]
        if xw.ndim != 2 or xw.shape[1] != self.config.window_samples:
            raise ValueError("输入窗口维度应为 [n_channels, window_samples]")

        X = xw[np.newaxis, :, :]

        # 分支A：MDM（跨带距离求和→softmax）
        dist = self._compute_mdm_distances(X)  # [1, 2]
        p_mdm = self._softmax_neg_dist(dist)[0]
        p_mdm = float(self._temp_mdm.transform(np.array([p_mdm]))[0])

        # 分支B（改造）：每子带独立TS+Clf → 中层融合概率
        band_signals = self._bank.transform(xw)  # List[[C, T]]
        p_bands: List[float] = []
        for b_idx, b in enumerate(band_signals):
            # EA 对齐
            if self._align_mode == "ea" and self._ea_whitener is not None and self._ea_whitener.shape[0] == b.shape[0]:
                b = (self._ea_whitener @ b)
            if self._channel_weights_vec is not None and self._channel_weights_vec.shape[0] == b.shape[0]:
                b = (self._channel_weights_vec[:, None] * b)
            # 🔧 Linus修复：去均值后计算协方差
            b_centered = b - np.mean(b, axis=1, keepdims=True)
            cov = self._cov.transform(b_centered[np.newaxis, :, :])   # [1, C, C]
            # RA 对齐
            if self._align_mode == "ra" and self._ra_whitener is not None and self._ra_whitener.shape[0] == cov.shape[1]:
                W = self._ra_whitener
                cov = (W[None, :, :] @ cov @ W[None, :, :].transpose(0, 2, 1))
            f = self._ts_band[b_idx].transform(cov)          # [1, n_feat_b]
            f = self._ts_scalers[b_idx].transform(f)         # 缩放对齐
            clf = self._ts_clfs[b_idx]
            if hasattr(clf, "predict_proba"):
                p_b = float(clf.predict_proba(f)[0, 1])
            else:
                # 兜底：用decision_function经sigmoid近似成概率
                s = float(clf.decision_function(f).ravel()[0])
                p_b = 1.0 / (1.0 + np.exp(-s))
            p_bands.append(p_b)

        # # 打印每子带概率（便于观察哪些子带容易拉高）
        # band_info = []
        # for i, (band_range, p_b, w_b) in enumerate(zip(self.config.subbands, p_bands, self.config.ts_band_weights)):
        #     band_info.append(f"B{i}({band_range[0]:.0f}-{band_range[1]:.0f}Hz):{p_b:.3f}*{w_b:.1f}")
        # print(f"[Bands] {', '.join(band_info)}")

        w_b = np.array(self.config.ts_band_weights, dtype=np.float64)
        if w_b.shape[0] != len(p_bands):
            w_b = np.ones(len(p_bands), dtype=np.float64)
        p_tslr = float(np.dot(p_bands, w_b) / max(1e-9, np.sum(w_b)))
        p_tslr = float(self._temp_tslr.transform(np.array([p_tslr]))[0])

        # 集成分类器预测
        result = {"p_mdm": float(p_mdm), "p_tslr": float(p_tslr)}

        if self.config.enable_ensemble:
            # FBCSP预测
            p_fbcsp = self._predict_fbcsp(xw)
            result["p_fbcsp"] = float(p_fbcsp)

            # 小波特征预测
            p_wavelet = self._predict_wavelet(xw)
            result["p_wavelet"] = float(p_wavelet)

            # 频谱特征预测
            p_spectral = self._predict_spectral(xw)
            result["p_spectral"] = float(p_spectral)

            # 集成融合
            weights = self.config.ensemble_weights
            p = (weights.get("mdm", 0.25) * p_mdm +
                 weights.get("tslr", 0.25) * p_tslr +
                 weights.get("fbcsp", 0.20) * p_fbcsp +
                 weights.get("wavelet", 0.15) * p_wavelet +
                 weights.get("spectral", 0.15) * p_spectral)

            # 归一化权重
            w_sum = sum(weights.values())
            if w_sum > 1e-9:
                p = p / w_sum
        else:
            # 原始融合
            w_mdm = float(self.config.fusion_weights.get("mdm", 0.6))
            w_tslr = float(self.config.fusion_weights.get("tslr", 0.4))
            w_sum = max(1e-9, w_mdm + w_tslr)
            p = (w_mdm * p_mdm + w_tslr * p_tslr) / w_sum

        p = float(np.clip(p, 0.0, 1.0))
        result["p"] = p
        return result

    def _predict_fbcsp(self, X_window: np.ndarray) -> float:
        """FBCSP分支预测"""
        if not self._fbcsp_models:
            return 0.5

        try:
            # 提取各子带特征并预测
            band_probs = []
            band_signals = self._bank.transform(X_window)

            for b_idx, (csp, lda) in enumerate(self._fbcsp_models):
                if b_idx < len(band_signals):
                    band_data = band_signals[b_idx][np.newaxis, :, :]  # [1, n_channels, n_samples]
                    csp_features = csp.transform(band_data)  # [1, n_components]
                    csp_features_scaled = self._fbcsp_scalers[b_idx].transform(csp_features)

                    if hasattr(lda, "predict_proba"):
                        p_band = float(lda.predict_proba(csp_features_scaled)[0, 1])
                    else:
                        # 备用方案
                        decision = float(lda.decision_function(csp_features_scaled)[0])
                        p_band = 1.0 / (1.0 + np.exp(-decision))

                    band_probs.append(p_band)

            # 子带概率平均
            if band_probs:
                return float(np.mean(band_probs))
            else:
                return 0.5

        except Exception:
            return 0.5

    def _predict_wavelet(self, X_window: np.ndarray) -> float:
        """小波特征分支预测"""
        if self._wavelet_model is None or self._wavelet_extractor is None:
            return 0.5

        try:
            features = self._wavelet_extractor.extract_features(X_window)
            features_scaled = self._wavelet_scaler.transform(features.reshape(1, -1))

            if hasattr(self._wavelet_model, "predict_proba"):
                return float(self._wavelet_model.predict_proba(features_scaled)[0, 1])
            else:
                decision = float(self._wavelet_model.decision_function(features_scaled)[0])
                return 1.0 / (1.0 + np.exp(-decision))

        except Exception:
            return 0.5

    def _predict_spectral(self, X_window: np.ndarray) -> float:
        """频谱特征分支预测"""
        if self._spectral_model is None or self._spectral_extractor is None:
            return 0.5

        try:
            features = self._spectral_extractor.extract_features(X_window)
            features_scaled = self._spectral_scaler.transform(features.reshape(1, -1))

            if hasattr(self._spectral_model, "predict_proba"):
                return float(self._spectral_model.predict_proba(features_scaled)[0, 1])
            else:
                decision = float(self._spectral_model.decision_function(features_scaled)[0])
                return 1.0 / (1.0 + np.exp(-decision))

        except Exception:
            return 0.5

    # -------------------- 持久化与状态 --------------------
    def get_status(self) -> Dict[str, Any]:
        return {
            "subbands": list(self.config.subbands),
            "covariance_estimator": self.config.covariance_estimator,
            "riemann_metric": self.config.riemann_metric,
            "fusion_weights": dict(self.config.fusion_weights),
            "ts_classifier": self.config.ts_classifier,
            "ts_band_weights": list(self.config.ts_band_weights or []),
            "enable_ensemble": self.config.enable_ensemble,
            "ensemble_weights": dict(self.config.ensemble_weights) if self.config.ensemble_weights else {},
            "is_fitted": self._is_fitted,
            "alignment": {
                "mode": self._align_mode or "none",
                "enabled": bool(self._align_mode is not None),
            },
            "temperature": {
                "mdm": float(self._temp_mdm.temperature),
                "tslr": float(self._temp_tslr.temperature),
            },
        }

    def save(self, filepath: str) -> None:
        """保存模型（自动加密）"""
        from utils.model_security import save_secure_model
        save_secure_model(self, filepath)

    @staticmethod
    def load(filepath: str) -> "PlanAClassifier":
        """加载模型（自动解密）"""
        from utils.model_security import load_secure_model
        obj = load_secure_model(filepath)
        if not isinstance(obj, PlanAClassifier):
            raise TypeError("加载的对象类型不匹配：期望 PlanAClassifier")
        return obj



