# Context
Filename: BCI_分类效果问题分析任务.md
Created On: 2025-01-20
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
分析脑机接口康复训练系统中运动想象分类效果不佳的问题：
1. 静息态数据校准导致概率变化很小，是否可以去掉？
2. 训练时和治疗时数据标准化的一致性问题
3. 运动想象与否跟输出概率没有关系的根本原因
4. 全面检查从训练到分类过程的处理流程
5. 分析现有分类算法的优化空间

# Project Overview
脑机接口康复训练系统 - 基于PySide6的医疗级EEG信号处理和康复治疗系统，采用Plan A方案，集成Filter-Bank Riemannian + TSLR分类器

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 系统架构关键发现

### 1. 静息态校准流程分析
**文件**: `ui/pages/treatment_page.py:2930-2954`
```python
def _finish_pre_treatment_calibration(self):
    # 收集1分钟静息态数据
    X_cal = np.stack(self._calib_windows, axis=0)  # [n_trials, 8, 250]
    
    # 调用PlanA管理器进行会话对齐（RA模式）
    info = self.weighted_voting_manager.calibrate_session(
        X_cal, y_cal=None, alignment_mode='ra', do_temp=False
    )
```

**核心机制**:
- 收集60秒静息态数据窗口
- 执行Riemannian Alignment (RA)协方差对齐
- 设置静息分位门控阈值
- 可选择性启用会话标准化

### 2. 数据标准化策略不一致问题

#### 训练阶段标准化
**文件**: `services/plan_a_classifier.py:738-744`
```python
def _train_ensemble_classifiers(self, X: np.ndarray, y: np.ndarray) -> None:
    X_processed = X
    if self.config.ensemble_use_session_norm:
        # 使用全局标准化（模拟会话标准化）
        X_processed = self._apply_global_normalization(X)
```

#### 实时分类阶段标准化
**文件**: `services/eeg_preprocessing/realtime_integration_manager.py:318-341`
```python
def get_current_window(self, duration_sec: float = 2.0) -> Optional[np.ndarray]:
    # 方案A：默认禁用窗口级标准化
    if getattr(self.config, 'enable_window_standardization', False):
        # 窗口级Z-score或稳健标准化
```

**关键发现**: 
- 预处理配置默认关闭所有标准化: `enable_window_standardization = False`
- 训练时可选全局标准化，但默认关闭: `ensemble_use_session_norm=False`  
- 会话标准化在PlanAManager中可控: `_enable_session_normalization = True`

### 3. 概率平滑机制分析
**文件**: `services/plan_a_manager.py:17-33, 52`
```python
@dataclass
class _EWMASmoother:
    enabled: bool = True
    alpha: float = 0.2  # 默认0.2，实际设置为0.35
    
    def update(self, p: float) -> float:
        if self._p is None:
            self._p = float(p)
        else:
            self._p = float(self.alpha * p + (1.0 - self.alpha) * self._p)
        return self._p
```

**当前设置**: EWMA alpha=0.35，意味着35%权重给新概率，65%权重给历史平滑值

### 4. 分类流程关键节点

#### 核心分类器架构
**文件**: `services/plan_a_classifier.py:827-925`
- **MDM分类器**: 基于Riemannian几何的协方差矩阵分类
- **TSLR分类器**: Tangent Space Logistic Regression  
- **集成分类器**: FBCSP + 小波特征 + 频谱特征（可选）
- **融合权重**: MDM(0.6) + TSLR(0.4)

#### 会话对齐机制问题
**文件**: `services/plan_a_classifier.py:541-570`
```python
def calibrate_session(self, X_cal: np.ndarray, mode: str = "ea"):
    # 计算静息态协方差均值
    cov_sum = np.zeros((n_channels, n_channels))
    for i in range(n_trials):
        x = X_cal[i]
        if self._channel_weights_vec is not None:
            x = (self._channel_weights_vec[:, None] * x)
        c = np.cov(x, bias=False)  # 样本协方差，不去均值
        cov_sum += c
    cov_mean = cov_sum / max(1, n_trials)
    
    W = self._sym_posdef_invsqrt(cov_mean)  # 计算逆平方根矩阵
```

## 发现的关键问题

### 问题1: 静息态校准可能过度约束分类器
- 静息态校准强制对所有数据进行协方差白化
- 可能消除了运动想象和静息态之间的本质差异
- 导致分类器输出概率变化很小

### 问题2: 数据标准化路径不一致  
- 训练数据可能未经标准化
- 实时数据根据配置可能经过不同的标准化
- 会话标准化在校准时才计算和应用

### 问题3: 概率平滑过度抑制变化
- EWMA alpha=0.35意味着历史权重过高(65%)
- 可能掩盖真实的分类概率变化
- 导致概率输出过于稳定

### 问题4: 协方差计算不去均值
- `np.cov(x, bias=False)` 使用样本协方差但不去均值
- 可能引入DC偏移影响
- 与标准Riemannian几何假设不符

## 深度分析结果

### 1. 通道权重配置分析
**当前设置**: `[0.7, 0.75, 0.75, 1.0, 1.0, 1.0, 1.0, 1.0]`
**通道对应**: `['PZ','P3','P4','C3','CZ','C4','F3','F4']`

**问题发现**:
- 下调了顶叶区域权重 (PZ/P3/P4: 0.7-0.75)
- 保持运动皮层权重为1.0 (C3/CZ/C4)
- 这可能不利于运动想象分类，因为运动想象主要激活运动皮层(C3/C4)和感觉运动皮层

### 2. 训练数据处理流程确认
**文件**: `services/training_session_manager.py:1027-1092`

**关键发现**:
- 训练数据直接使用原始EEG窗口: `X_raw = np.array(self.collected_data)`
- 经过滑窗增强: 4秒数据→多个2秒窗口
- 应用样本清洁和均衡处理
- **未发现明确的标准化步骤**

### 3. 分类器训练过程分析
**文件**: `services/plan_a_classifier.py:660-732`

**训练流程**:
1. 应用通道权重: `b = (self._channel_weights_vec[:, None] * b)`
2. 计算协方差: `cov = self._cov.fit_transform(b[np.newaxis, :, :])`
3. MDM训练: 每子带独立训练
4. TSLR训练: TangentSpace特征 + StandardScaler + LDA/LR
5. 集成分类器训练: 可选启用，默认关闭会话标准化

**关键问题**: TSLR分支使用StandardScaler，但MDM分支未标准化

### 4. 实时分类数据流分析
**实时窗口获取**: `realtime_integration_manager.py:299-342`
- 默认禁用窗口级标准化: `enable_window_standardization = False`
- 可选稳健标准化或Z-score标准化
- 会话标准化在PlanAManager中控制

### 5. 静息态校准机制深度分析
**校准流程**: `ui/pages/treatment_page.py:2930-2954`
1. 收集60秒静息态数据
2. 执行Riemannian Alignment (RA模式)
3. 计算协方差白化矩阵
4. 设置静息分位门控阈值

**RA对齐机制**: `services/plan_a_classifier.py:652-654`
```python
if self._align_mode == "ra" and self._ra_whitener is not None:
    W = self._ra_whitener
    cov = (W[None, :, :] @ cov @ W[None, :, :].transpose(0, 2, 1))
```

**问题分析**:
- RA对齐强制将所有协方差矩阵向静息态协方差对齐
- 可能消除了运动想象和静息态的固有差异
- 导致分类器难以区分两种状态

## 核心问题总结

### 主要问题
1. **静息态校准过度约束**: RA对齐可能消除了有用的类间差异
2. **标准化不一致**: 训练未标准化，实时分类可能标准化
3. **通道权重设置不当**: 下调了运动皮层相关区域权重
4. **概率平滑过度**: EWMA α=0.35导致历史权重过高(65%)
5. **协方差计算问题**: 未去均值可能引入DC偏移
