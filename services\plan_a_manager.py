#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plan A Manager: 提供与UI对接的最小接口，保持与现有管理器相似的方法。
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Optional
import numpy as np

from .plan_a_classifier import PlanAClassifier, PlanAConfig


@dataclass
class _EWMASmoother:
    enabled: bool = True
    alpha: float = 0.2
    _p: Optional[float] = None

    def reset(self) -> None:
        self._p = None

    def update(self, p: float) -> float:
        if not self.enabled:
            return p
        if self._p is None:
            self._p = float(p)
        else:
            self._p = float(self.alpha * p + (1.0 - self.alpha) * self._p)
        return self._p


class PlanAManager:
    def __init__(self, clf: Optional[PlanAClassifier] = None, config: Optional[PlanAConfig] = None) -> None:
        self.classifier = clf or PlanAClassifier(config=config)
        self._is_fitted = getattr(self.classifier, "_is_fitted", False)

        # 难度-阈值映射（与UI一致）
        self._difficulty_threshold = {
            1: 0.55,
            2: 0.60,
            3: 0.70,
            4: 0.75,
            5: 0.80,
        }
        self._current_difficulty = 3
        self._custom_trigger_threshold: Optional[float] = None

        # 概率平滑（仅概率层，不改变阈值语义）
        # 🔧 Linus修复：进一步提高响应性，alpha=0.9让新概率占主导
        self._smoother = _EWMASmoother(enabled=True, alpha=0.9)

        # 调试
        self._debug = False

        # 门控参数（默认开启，数值为保守设置）
        self._consistency_enabled = True
        self._consistency_min_agreement = 0.15  # agreement = 1 - |p_mdm - p_tslr|

        self._dwell_enabled = False
        self._min_consecutive_frames = 2
        self._consecutive_counter = 0

        self._rest_gate_enabled = True
        self._rest_percentile = 0.95
        self._rest_gate_threshold_q: Optional[float] = None

        # 会话统一标准化（可开关）
        # 🔧 Linus修复：禁用会话标准化，因为已在训练时强制标准化
        self._enable_session_normalization = False
        self._session_norm = None  # type: Optional[dict]

    # ---------------- 接口：训练/加载 ----------------
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        self.classifier.fit(X, y)
        self._is_fitted = True

    # ---------------- 接口：UI参数 ----------------
    def set_difficulty_level(self, level: int) -> None:
        if level in self._difficulty_threshold:
            self._current_difficulty = level
            # 如设置了自定义阈值，不覆盖

    def set_custom_thresholds(self, trigger_threshold: Optional[float] = None) -> None:
        if trigger_threshold is not None:
            self._custom_trigger_threshold = float(trigger_threshold)

    def get_current_thresholds(self) -> float:
        if self._custom_trigger_threshold is not None:
            return float(self._custom_trigger_threshold)
        return float(self._difficulty_threshold.get(self._current_difficulty, 0.70))

    # ---------------- 接口：平滑与调试 ----------------
    def set_temporal_smoothing(self, enabled: bool, method: str = "ewma", alpha: float = 0.2) -> None:
        # 仅支持ewma；保持接口兼容
        self._smoother.enabled = bool(enabled)
        self._smoother.alpha = float(alpha)

    def _apply_session_normalization(self, X: np.ndarray) -> np.ndarray:
        if not (getattr(self, '_enable_session_normalization', False) and getattr(self, '_session_norm', None) is not None):
            return X
        c = self._session_norm.get('center', None)
        s = self._session_norm.get('scale', None)
        if c is None or s is None:
            return X
        # 兼容 [C, T] 或 [1, C, T]
        if X.ndim == 2:
            return (X - c) / s
        if X.ndim == 3 and X.shape[0] == 1:
            return (X[0] - c) / s
        return X

    def reset_temporal_smoother(self) -> None:
        self._smoother.reset()

    def set_realtime_debug(self, enabled: bool = True) -> None:
        self._debug = bool(enabled)

    # ---------------- 接口：预测 ----------------
    def predict_with_details(self, X_window: np.ndarray, simple_output: bool = False) -> Dict[str, Any]:
        if not self._is_fitted:
            raise ValueError("PlanAManager: 模型未训练")

        comps = {}
        try:
            # 🔧 Linus修复：强制应用与训练时相同的全局标准化
            Xw = self.classifier._apply_global_normalization(X_window[np.newaxis, :, :])[0]
            comps = self.classifier.predict_proba_components(Xw)
            p = float(comps.get("p", 0.5))
            p_mdm = float(comps.get("p_mdm", 0.5))
            p_tslr = float(comps.get("p_tslr", 0.5))
        except AttributeError:
            # 兼容旧版本分类器（无组件输出）
            p = self.classifier.predict_proba(X_window)
            p_mdm = float('nan')
            p_tslr = float('nan')

        p_sm = self._smoother.update(p)
        threshold = self.get_current_thresholds()

        # 一致性门：agreement = 1 - |p_mdm - p_tslr|
        agreement = 1.0 - float(abs(p_mdm - p_tslr)) if (not np.isnan(p_mdm) and not np.isnan(p_tslr)) else float('nan')
        g_consistency_ok = True
        if self._consistency_enabled and not np.isnan(agreement):
            g_consistency_ok = (agreement >= float(self._consistency_min_agreement))

        # 静息分位门：在阈值基础上取更严格者
        thr_gate = float(threshold)
        if self._rest_gate_enabled and self._rest_gate_threshold_q is not None:
            q = float(self._rest_gate_threshold_q)
            thr_cap = 0.85
            thr_gate = float(min(thr_cap, max(threshold, q)))

        # 持续计数门：仅当通过一致性门且超过 thr_gate 才累积
        if (p_sm >= thr_gate) and g_consistency_ok:
            self._consecutive_counter += 1
        else:
            self._consecutive_counter = 0

        if self._dwell_enabled:
            trigger = (self._consecutive_counter >= int(self._min_consecutive_frames))
        else:
            trigger = ((p_sm >= thr_gate) and g_consistency_ok)
        final_prediction = int(p_sm >= 0.5)
        final_probability = np.array([1.0 - p_sm, p_sm], dtype=np.float32)

        if self._debug:
            pm = 'nan' if np.isnan(p_mdm) else f"{p_mdm:.3f}"
            pt = 'nan' if np.isnan(p_tslr) else f"{p_tslr:.3f}"
            qa = 'nan' if np.isnan(agreement) else f"{agreement:.3f}"
            qrest = 'na' if self._rest_gate_threshold_q is None else f"{self._rest_gate_threshold_q:.3f}"

            # 集成分类器概率输出
            ensemble_info = ""
            if hasattr(self.classifier.config, 'enable_ensemble') and self.classifier.config.enable_ensemble:
                p_fbcsp = comps.get("p_fbcsp", float('nan'))
                p_wavelet = comps.get("p_wavelet", float('nan'))
                p_spectral = comps.get("p_spectral", float('nan'))

                pf = 'nan' if np.isnan(p_fbcsp) else f"{p_fbcsp:.3f}"
                pw = 'nan' if np.isnan(p_wavelet) else f"{p_wavelet:.3f}"
                ps = 'nan' if np.isnan(p_spectral) else f"{p_spectral:.3f}"

                ensemble_info = f", p_fbcsp={pf}, p_wavelet={pw}, p_spectral={ps}"

            print(
                f"[PlanA] p={p:.3f}, p_mdm={pm}, p_tslr={pt}{ensemble_info}, p_ewma={p_sm:.3f}, "
                f"thr_base={threshold:.3f}, thr_gate={thr_gate:.3f}, agree={qa}, "
                f"rest_q={qrest}, dwell={self._consecutive_counter}, trigger={trigger}"
            )

        return {
            "final_prediction": final_prediction,
            "final_probability": final_probability,
            "confidence": float(np.max(final_probability)),
            "individual_predictions": {"mdm": p_mdm, "tslr": p_tslr},
            "individual_probabilities": {"mdm": p_mdm, "tslr": p_tslr},
            "voting_weights": {},
            "trigger_decision": bool(trigger),
            "difficulty_level": self._current_difficulty,
            "agreement_level": "na",
            "classification_details": (
                f"p={p:.3f}, p_mdm={p_mdm:.3f}, p_tslr={p_tslr:.3f}, p_ewma={p_sm:.3f}, "
                f"thr_base={threshold:.3f}, thr_gate={thr_gate:.3f}, agreement={(0.0 if np.isnan(agreement) else agreement):.3f}, "
                f"rest_q={'na' if self._rest_gate_threshold_q is None else f'{self._rest_gate_threshold_q:.3f}'}, "
                f"dwell={self._consecutive_counter}, trigger={trigger}"
            ),
            "simple_result": (
                f"MI {p_sm:.3f} (thr={threshold:.3f})"
            ),
        }

    # ---------------- 接口：会话校准与门控配置 ----------------
    def calibrate_session(self, X_cal: np.ndarray, y_cal: Optional[np.ndarray] = None,
                          alignment_mode: str = "ea", do_temp: bool = False) -> Dict[str, Any]:
        if X_cal.ndim != 3 or X_cal.shape[2] != self.classifier.config.window_samples:
            raise ValueError("X_cal 维度应为 [n_trials, n_channels, window_samples]")

        # 先计算会话统一标准化参数（若启用）并标准化静息数据
        if getattr(self, '_enable_session_normalization', False):
            try:
                self._session_norm = self._compute_session_norm(X_cal)
                # 将标准化后的 X_cal 用于后续分位与对齐
                c = self._session_norm["center"]; s = self._session_norm["scale"]
                X_cal_norm = (X_cal - c) / s
            except Exception as _e:
                print(f"⚠️ 会话标准化参数计算失败: {_e}")
                X_cal_norm = X_cal
        else:
            X_cal_norm = X_cal

        # 对齐（改为 RA 优先）
        mode_eff = (alignment_mode or 'ra').lower()
        align_info = self.classifier.calibrate_session(X_cal_norm, y_cal=y_cal, mode=mode_eff)

        # 可选：温度校准（需要标签）
        temp_info = None
        if do_temp and y_cal is not None:
            try:
                temp_info = self.classifier.calibrate_temperature(X_cal, y_cal)
            except Exception:
                temp_info = None

        # 基于静息校准集估计 fused 概率分位（先做异常窗口剔除：5分位-95分位外移除）
        try:
            # 1) 计算每窗口的整体峰峰值幅度（跨所有通道与时间）
            amp_ranges: np.ndarray = np.zeros((X_cal.shape[0],), dtype=np.float64)
            for i in range(X_cal.shape[0]):
                w = X_cal[i]
                amp_ranges[i] = float(np.ptp(w, axis=None))

            # 2) 计算5%与95%分位，生成掩码
            low_q = float(np.quantile(amp_ranges, 0.05))
            high_q = float(np.quantile(amp_ranges, 0.95))
            keep_mask = (amp_ranges >= low_q) & (amp_ranges <= high_q)

            # 若全被剔除或样本过少，则退化为保留全部
            if int(np.sum(keep_mask)) < max(5, int(0.2 * X_cal.shape[0])):
                keep_mask = np.ones_like(amp_ranges, dtype=bool)

            # 3) 对保留窗口计算 fused 概率
            fused_list = []
            for i in range(X_cal.shape[0]):
                if not keep_mask[i]:
                    continue
                comps = self.classifier.predict_proba_components((X_cal_norm[i] if 'X_cal_norm' in locals() else X_cal[i]))
                fused_list.append(float(comps.get("p", 0.5)))

            if len(fused_list) > 0:
                q = float(np.quantile(np.asarray(fused_list, dtype=np.float64), float(self._rest_percentile)))
                self._rest_gate_threshold_q = q
            else:
                self._rest_gate_threshold_q = None

            rest_info_extra = {
                "rest_windows_total": int(X_cal.shape[0]),
                "rest_windows_used": int(np.sum(keep_mask)),
                "amp_range_q05": low_q,
                "amp_range_q95": high_q,
            }
        except Exception:
            # 回退：不做剔除，计算全部窗口的分位
            fused_list = []
            for i in range(X_cal.shape[0]):
                comps = self.classifier.predict_proba_components((X_cal_norm[i] if 'X_cal_norm' in locals() else X_cal[i]))
                fused_list.append(float(comps.get("p", 0.5)))
            if len(fused_list) > 0:
                q = float(np.quantile(np.asarray(fused_list, dtype=np.float64), float(self._rest_percentile)))
                self._rest_gate_threshold_q = q
            else:
                self._rest_gate_threshold_q = None
            rest_info_extra = {
                "rest_windows_total": int(X_cal.shape[0]),
                "rest_windows_used": int(X_cal.shape[0]),
                "amp_range_q05": None,
                "amp_range_q95": None,
            }
    def _compute_session_norm(self, X_cal: np.ndarray) -> dict:
        # X_cal: [N, C, T]
        # 裁剪1%-99%并计算robust median/MAD，MAD*1.4826 ~ std
        Xc = X_cal.reshape(-1, X_cal.shape[1], X_cal.shape[2])  # [N, C, T]
        # 按通道聚合所有时间样本
        # 先计算通道的1%-99%分位阈
        p1 = np.percentile(Xc, 1, axis=(0, 2), keepdims=True)
        p99 = np.percentile(Xc, 99, axis=(0, 2), keepdims=True)
        Xclip = np.clip(Xc, p1, p99)
        med = np.median(Xclip, axis=(0, 2), keepdims=True)    # [1,C,1]
        mad = np.median(np.abs(Xclip - med), axis=(0, 2), keepdims=True) * 1.4826
        eps = 1e-8
        mad = np.maximum(mad, eps)
        # 下限夹紧：以通道mad的第10分位为下限
        mad_flat = mad.reshape(1, -1)
        mad_p10 = np.percentile(mad_flat, 10)
        mad = np.maximum(mad, mad_p10)
        return {"center": med.squeeze(0), "scale": mad.squeeze(0), "eps": eps}

        # 重置门控状态
        self.reset_gating_state()

        info: Dict[str, Any] = {
            "alignment": align_info.get("alignment", {"enabled": False}),
            "temperature": ({} if temp_info is None else temp_info),
            "rest_percentile": self._rest_percentile,
            "rest_gate_threshold_q": self._rest_gate_threshold_q,
        }
        info.update(rest_info_extra)
        return info

    def set_gating_params(self,
                          consistency_min_agreement: Optional[float] = None,
                          min_consecutive_frames: Optional[int] = None,
                          rest_percentile: Optional[float] = None,
                          enable_consistency: Optional[bool] = None,
                          enable_rest_gate: Optional[bool] = None,
                          enable_dwell: Optional[bool] = None) -> None:
        if consistency_min_agreement is not None:
            self._consistency_min_agreement = float(consistency_min_agreement)
        if min_consecutive_frames is not None:
            self._min_consecutive_frames = int(min_consecutive_frames)
        if rest_percentile is not None:
            self._rest_percentile = float(rest_percentile)
        if enable_consistency is not None:
            self._consistency_enabled = bool(enable_consistency)
        if enable_rest_gate is not None:
            self._rest_gate_enabled = bool(enable_rest_gate)
        if enable_dwell is not None:
            self._dwell_enabled = bool(enable_dwell)

    def reset_gating_state(self) -> None:
        self._consecutive_counter = 0
        self._smoother.reset()

    # ---------------- 接口：状态 ----------------
    def get_system_status(self) -> Dict[str, Any]:
        st = self.classifier.get_status()
        st.update({
            "threshold": self.get_current_thresholds(),
            "difficulty_level": self._current_difficulty,
            "smoothing": {
                "enabled": self._smoother.enabled,
                "alpha": self._smoother.alpha,
            },
            "gating": {
                "consistency_enabled": self._consistency_enabled,
                "consistency_min_agreement": self._consistency_min_agreement,
                "dwell_enabled": self._dwell_enabled,
                "min_consecutive_frames": self._min_consecutive_frames,
                "rest_gate_enabled": self._rest_gate_enabled,
                "rest_percentile": self._rest_percentile,
                "rest_gate_threshold_q": self._rest_gate_threshold_q,
            },
        })
        return st


